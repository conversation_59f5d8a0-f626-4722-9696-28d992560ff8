from fastapi import FastAP<PERSON>, responses
from fastapi.middleware.cors import CORSMiddleware
from app.v1.api import router as v1_api
from app.v1.api.auth_service import app as auth_service
# from app.v1.api.socket_service import app as socket_service
from app.v1.api.management_service import app as management_service
from app.v1.api.scheduler_service import app as scheduler_service
from app.v2.api.socket_service_v2 import app as socket_service_v2
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

app = FastAPI(
    title="Nepali App",
    description="A microservices-based application for language learning with quiz functionality",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    swagger_ui_parameters={
        "deepLinking": True,
        "persistAuthorization": True,
        "displayOperationId": True,
        "defaultModelsExpandDepth": -1  # Hide schemas section by default
    }
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Mount the v1 API router (API Gateway)

app.mount("/v1/auth", auth_service)
# app.mount("/v1/socket", socket_service)
app.mount("/v1/management", management_service)
app.mount("/v1/scheduler", scheduler_service)
app.mount("/v2/socket", socket_service_v2)

# Mount the microservices

# Set up combined OpenAPI documentation
services = {
    "auth": auth_service,
    # "socket": socket_service,
    "management": management_service
}

@app.get("/")
async def root():
    """Redirect to API documentation"""
    return responses.RedirectResponse(url="/docs")

@app.get("/v1")
async def v1_root():
    """Redirect to API documentation"""
    loggers.info("V1 root endpoint accessed, redirecting to docs")
    return responses.RedirectResponse(url="/docs")

# Add a custom endpoint to get service information
@app.get("/api/services")
async def get_services_info():
    """
    Get information about all available microservices.

    Returns:
        Dictionary with service information
    """
    return {
        "services": [
            {
                "name": "auth_service",
                "title": auth_service.title,
                "description": auth_service.description,
                "version": auth_service.version,
                "endpoint": "/v1/auth"
            },
            # {
            #     "name": "socket_service",
            #     "title": socket_service.title,
            #     "description": socket_service.description,
            #     "version": socket_service.version,
            #     "endpoint": "/v1/socket"
            # },
            {
                "name": "management_service",
                "title": management_service.title,
                "description": management_service.description,
                "version": management_service.version,
                "endpoint": "/v1/management"
            }
        ]
    }




@app.get("/v1/health")
async def health_check():
    """Minimal health check endpoint"""
    return {"status": "healthy"}

@app.get("/health")
async def root_health_check():
    """Minimal health check endpoint"""
    return {"status": "healthy"}
