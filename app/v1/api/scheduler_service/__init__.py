"""
Scheduler Service - Background Task Scheduling and Follow-up Generation

This service handles:
- Background task scheduling for follow-up generation
- Monitoring task completion status
- Automatic follow-up task generation when primary tasks complete
- Redis-based lightweight scheduling without disrupting existing code

Key Features:
- Lightweight Redis-based scheduling
- Automatic follow-up generation for completed PRIMARY task sets
- User context preservation through Redis
- Non-disruptive background processing
- Simple REST API for scheduling operations
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from datetime import datetime
import os

from app.shared.utils.logger import setup_new_logging
from app.shared.redis.redis_manager import RedisManager
from app.shared.scheduling.followup_scheduler import get_followup_scheduler

# Import routes
from app.v1.api.scheduler_service.routes.scheduler import router as scheduler_router
from app.v1.api.scheduler_service.routes.followup import router as followup_router

# Configure logging
logger = setup_new_logging(__name__)

# Global instances
redis_manager = None
followup_scheduler = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown."""
    global redis_manager, followup_scheduler

    try:
        logger.info("🚀 Starting Scheduler Service...")

        # Initialize Redis
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_manager = RedisManager(redis_url=redis_url)
        await redis_manager.ensure_connected()
        logger.info("✅ Redis connection established")

        # Initialize Follow-up Scheduler
        followup_scheduler = await get_followup_scheduler(redis_manager)
        logger.info("✅ Follow-up scheduler initialized and started")

        logger.info("🎯 Scheduler Service startup completed")
        yield

    except Exception as e:
        logger.error(f"❌ Scheduler Service startup failed: {e}")
        raise
    finally:
        logger.info("🔄 Scheduler Service shutting down...")

        # Stop scheduler
        if followup_scheduler:
            await followup_scheduler.stop_scheduler()
            logger.info("✅ Follow-up scheduler stopped")

        # Close Redis connections
        if redis_manager:
            await redis_manager.disconnect()
            logger.info("✅ Redis connections closed")

        logger.info("✅ Scheduler Service shutdown completed")


# Create FastAPI instance
app = FastAPI(
    title="Nepali App - Scheduler Service",
    description="Background task scheduling service for follow-up generation and task monitoring",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v1/scheduler/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    servers=[
        {
            "url": "/v1/scheduler",
            "description": "Scheduler Service API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(scheduler_router, tags=["Scheduler Operations"])
app.include_router(followup_router, tags=["Follow-up Generation"])


@app.get("/")
async def root():
    """Scheduler service root endpoint."""
    return {
        "service": "Nepali App - Scheduler Service",
        "version": "1.0.0",
        "description": "Background task scheduling service for follow-up generation",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """Scheduler service health check endpoint."""
    global redis_manager, followup_scheduler
    
    try:
        health_status = {
            "service": "scheduler_service",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }

        # Check Redis connection
        if redis_manager:
            redis_healthy = await redis_manager.ping()
            health_status["components"]["redis"] = {
                "status": "healthy" if redis_healthy else "unhealthy"
            }
        else:
            health_status["components"]["redis"] = {"status": "not_initialized"}

        # Check scheduler status
        if followup_scheduler:
            health_status["components"]["followup_scheduler"] = {
                "status": "running" if followup_scheduler.is_running else "stopped"
            }
        else:
            health_status["components"]["followup_scheduler"] = {"status": "not_initialized"}

        # Overall status
        all_healthy = all(
            comp.get("status") in ["healthy", "running"] 
            for comp in health_status["components"].values()
        )
        health_status["status"] = "healthy" if all_healthy else "degraded"

        return health_status

    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "service": "scheduler_service",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/status")
async def scheduler_status():
    """Get detailed scheduler status and statistics."""
    global followup_scheduler
    
    try:
        if not followup_scheduler:
            return {
                "scheduler": "not_initialized",
                "timestamp": datetime.now().isoformat()
            }

        # Get scheduler statistics
        stats = await followup_scheduler.get_scheduler_stats()
        
        return {
            "scheduler": "running" if followup_scheduler.is_running else "stopped",
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to get scheduler status: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Failed to get scheduler status",
                "details": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )
