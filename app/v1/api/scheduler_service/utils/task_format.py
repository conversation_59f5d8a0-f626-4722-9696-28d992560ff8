from bson import ObjectId
from typing import Dict, Any, List, Union
from app.shared.models.user import UserTenantDB
async def get_tasks_in_json_format(task_item_ids: List[Union[str, ObjectId]], current_user: UserTenantDB)-> str:
    """Fetch tasks in JSON format for a given task set."""

    pipeline = [
    {"$match": {"_id": {"$in": [ObjectId(task_id) for task_id in task_item_ids]}}},
    {"$project": {
        "question": 1,
        "correct_answer": 1
    }}
]

    task_items = await (await current_user.async_db.task_items.aggregate(pipeline)).to_list(length=None)
    # make proper single string for the questions
    tasks = ""
    tasks = ""
    for task in task_items:
        task["question"].pop("media_url", None)
        task["question"].pop("metadata", None)
        tasks += f"Question: {task['question']}\nAnswer: {task['correct_answer']}\n\n"

    return tasks
