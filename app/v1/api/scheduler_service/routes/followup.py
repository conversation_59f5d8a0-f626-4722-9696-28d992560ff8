"""
Follow-up Generation Routes - Follow-up Task Generation

Handles:
- Follow-up task generation for completed task sets
- Audio processing and Gemini AI integration
- Task creation and database storage
- User context management through Redis
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio
import os
import json
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import GenerationType
from app.v1.api.scheduler_service.utils.task_format import get_tasks_in_json_format

# Gemini AI imports
import google.generativeai as genai
from google.generativeai import types

# Configure logging
logger = setup_new_logging(__name__)

router = APIRouter(prefix="/api/followup")


class GenerateFollowupRequest(BaseModel):
    """Request model for follow-up generation."""
    task_set_id: str = Field(..., description="Original task set ID")
    user_id: str = Field(..., description="User ID")
    original_audio_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Original audio metadata"
    )


class GenerateFollowupResponse(BaseModel):
    """Response model for follow-up generation."""
    status: str = Field(..., description="Generation status")
    task_set_id: str = Field(..., description="Original task set ID")
    followup_tasks: Optional[str] = Field(None, description="Generated follow-up tasks JSON")
    usage_metadata: Optional[Dict[str, Any]] = Field(None, description="AI usage metadata")
    message: str = Field(..., description="Status message")
    generated_at: str = Field(..., description="When tasks were generated")


async def get_user_from_redis(user_id: str) -> UserTenantDB:
    """
    Get user context from Redis or create minimal context.
    
    This function retrieves user context that was stored in Redis
    instead of creating a new UserTenantDB instance.
    """
    try:
        # Import here to avoid circular imports
        from app.v1.api.scheduler_service import redis_manager
        
        if not redis_manager:
            raise HTTPException(
                status_code=503,
                detail="Redis manager not available"
            )
        
        # Try to get user context from Redis
        user_context_key = f"user_context:{user_id}"
        user_context_data = await redis_manager.get(user_context_key)
        
        if user_context_data:
            # Parse stored user context
            context = json.loads(user_context_data)
            logger.info(f"✅ Retrieved user context from Redis for user {user_id}")
            
            # Create UserTenantDB with stored context
            return UserTenantDB(
                user_id=user_id,
                tenant_id=context.get("tenant_id"),
                # Add other context fields as needed
            )
        else:
            # Create minimal user context and store in Redis for future use
            logger.info(f"⚠️ No user context in Redis, creating minimal context for user {user_id}")
            
            user = UserTenantDB(user_id=user_id)
            
            # Store minimal context in Redis for future use
            context_data = {
                "user_id": user_id,
                "tenant_id": getattr(user, 'tenant_id', None),
                "created_at": datetime.now().isoformat()
            }
            
            await redis_manager.set(
                user_context_key,
                json.dumps(context_data),
                ex=3600  # Expire after 1 hour
            )
            
            return user
            
    except Exception as e:
        logger.error(f"❌ Error getting user context: {e}")
        # Fallback to creating new user context
        return UserTenantDB(user_id=user_id)


async def generate_with_gemini(audio_bytes: bytes, old_tasks: str) -> Dict[str, Any]:
    """
    Generate follow-up tasks using Gemini AI.
    
    Args:
        audio_bytes: Original audio data
        old_tasks: Previous tasks in JSON format
        
    Returns:
        Dictionary with generated tasks and usage metadata
    """
    try:
        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.0-flash"
        contents = [
            types.Part.from_bytes(
                mime_type="audio/mp4",
                data=audio_bytes
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text="""You are a Nepali language tutor designing interactive quiz tasks for **{{age}}-year-old children**.
### Instructions:

You are given a **Nepali audio recording**.

Your task is to generate exactly **{{num_tasks}} quiz questions** based **only on what is clearly said in the audio**.

Do **not** add fictional details or imagined content.
Only include things **explicitly present in the audio**.

### Question Format:

Each task must follow this question-type cycle:
1. `"single_choice"`
2. `"multiple_choice"`
3. `"image_identification"`
4. `"speak_word"`

(Repeat this order if `num_tasks` > 4)

###  Quiz Question Structure:

```json
{
"type": "single_choice" | "multiple_choice" | "image_identification" | "speak_word",
"text": "Question in Nepali",
"translated_text": "English translation",
"options": {
"a": "घोड़ा",
"b": "कुकुर",
"c": "हात्ती"
}, // Omit for speak_word
"answer_hint": "elephant", // or Nepali word like "हात्ती" for speak_word
"answer": "a" // or ["a", "b"] for multiple_choice; omit for speak_word
}


 Guidelines:
Use simple, vivid Nepali suited for children.
All content must be directly grounded in the audio.
For "speak_word":

Only one Nepali word (no phrases or sentences).
For "image_identification":

Focus on concrete objects/animals mentioned in the audio.
Return exactly **{{num_tasks}}** tasks in this JSON array format:

```json
[
  { task1 },
  { task2 },
  ...
]
```"""),
            ],
        )

        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        if not response or not response.candidates:
            raise ValueError("No candidates found in the response.")
        
        tasks = response.candidates[0].content.parts[0].text
        if not tasks:
            raise ValueError("No tasks generated in the response.")
        usage = response.candidates[0].usage
        
        return {
            "tasks": tasks,
            "usage": usage
        }
        
    except Exception as e:
        logger.error(f"❌ Error generating with Gemini: {e}")
        raise


@router.post("/generate", response_model=GenerateFollowupResponse)
async def generate_followup_tasks(request: GenerateFollowupRequest):
    """
    Generate follow-up tasks for a completed task set.
    
    This endpoint:
    1. Retrieves user context from Redis
    2. Gets original audio and task data
    3. Generates follow-up tasks using Gemini AI
    4. Returns generated tasks for further processing
    """
    try:
        logger.info(f"🎯 Generating follow-up tasks for task_set {request.task_set_id}")
        
        # Get user context from Redis
        current_user = await get_user_from_redis(request.user_id)
        
        # Get task set information
        tasks_info = await current_user.async_db.task_set.find_one(
            {"_id": ObjectId(request.task_set_id)},
        )
        
        if not tasks_info:
            raise HTTPException(
                status_code=404,
                detail=f"Task set {request.task_set_id} not found"
            )
        
        # Get audio bytes
        audio_object_name = tasks_info["input_content"]["object_name"]
        audiobytes = await asyncio.to_thread(
            current_user.minio.get_audio_bytes, 
            audio_object_name
        )
        
        if not audiobytes:
            raise HTTPException(
                status_code=400,
                detail="Audio bytes not found for the given task set"
            )
        
        # Get old tasks for context
        old_tasks = await get_tasks_in_json_format(
            task_set_id=tasks_info["tasks"],
            current_user=current_user,
        )
        
        # Generate follow-up tasks using Gemini
        result = await generate_with_gemini(audiobytes, old_tasks)
        
        logger.info(f"✅ Successfully generated follow-up tasks for task_set {request.task_set_id}")
        
        return GenerateFollowupResponse(
            status="success",
            task_set_id=request.task_set_id,
            followup_tasks=result.get("tasks"),
            usage_metadata=result.get("usage"),
            message="Follow-up tasks generated successfully",
            generated_at=datetime.now().isoformat()
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"❌ Error generating follow-up tasks for {request.task_set_id}: {e}")
        
        return GenerateFollowupResponse(
            status="error",
            task_set_id=request.task_set_id,
            followup_tasks=None,
            usage_metadata=None,
            message=f"Failed to generate follow-up tasks: {str(e)}",
            generated_at=datetime.now().isoformat()
        )


@router.get("/test-user-context/{user_id}")
async def test_user_context(user_id: str):
    """
    Test endpoint to verify user context retrieval from Redis.
    
    This is a utility endpoint for testing the Redis user context functionality.
    """
    try:
        user = await get_user_from_redis(user_id)
        
        return {
            "user_id": user_id,
            "user_context_available": True,
            "tenant_id": getattr(user, 'tenant_id', None),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing user context for {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user context: {str(e)}"
        )
