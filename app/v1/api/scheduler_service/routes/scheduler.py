"""
Scheduler Routes - Background Task Scheduling Operations

Handles:
- Scheduling follow-up generation for completed task sets
- Monitoring scheduler status and statistics
- Manual scheduler control operations
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.scheduling.followup_scheduler import get_followup_scheduler
from app.shared.redis.redis_manager import RedisManager

# Configure logging
logger = setup_new_logging(__name__)

router = APIRouter(prefix="/api")


class ScheduleFollowupRequest(BaseModel):
    """Request model for scheduling follow-up generation."""
    task_set_id: str = Field(..., description="Completed task set ID")
    user_id: str = Field(..., description="User ID")
    original_audio_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Original audio metadata for follow-up generation"
    )


class ScheduleFollowupResponse(BaseModel):
    """Response model for follow-up scheduling."""
    success: bool = Field(..., description="Whether scheduling was successful")
    task_set_id: str = Field(..., description="Task set ID")
    message: str = Field(..., description="Status message")
    scheduled_at: str = Field(..., description="When the task was scheduled")


async def get_scheduler_dependency():
    """Dependency to get the followup scheduler instance."""
    # Import here to avoid circular imports
    from app.v1.api.scheduler_service import followup_scheduler
    
    if not followup_scheduler:
        raise HTTPException(
            status_code=503,
            detail="Scheduler service not initialized"
        )
    
    return followup_scheduler


@router.post("/schedule-followup", response_model=ScheduleFollowupResponse)
async def schedule_followup_generation(
    request: ScheduleFollowupRequest,
    scheduler = Depends(get_scheduler_dependency)
):
    """
    Schedule follow-up task generation for a completed task set.
    
    This endpoint is called when a PRIMARY task set is completed to
    automatically schedule follow-up task generation in the background.
    """
    try:
        logger.info(f"📅 Scheduling follow-up generation for task_set {request.task_set_id}")
        
        # Schedule the follow-up generation
        success = await scheduler.schedule_followup_generation(
            task_set_id=request.task_set_id,
            user_id=request.user_id,
            original_audio_info=request.original_audio_info or {}
        )
        
        if success:
            logger.info(f"✅ Successfully scheduled follow-up for task_set {request.task_set_id}")
            return ScheduleFollowupResponse(
                success=True,
                task_set_id=request.task_set_id,
                message="Follow-up generation scheduled successfully",
                scheduled_at=datetime.now().isoformat()
            )
        else:
            logger.warning(f"⚠️ Failed to schedule follow-up for task_set {request.task_set_id}")
            return ScheduleFollowupResponse(
                success=False,
                task_set_id=request.task_set_id,
                message="Failed to schedule follow-up generation",
                scheduled_at=datetime.now().isoformat()
            )
            
    except Exception as e:
        logger.error(f"❌ Error scheduling follow-up for task_set {request.task_set_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to schedule follow-up generation: {str(e)}"
        )


@router.get("/scheduler-stats")
async def get_scheduler_statistics(
    scheduler = Depends(get_scheduler_dependency)
):
    """
    Get detailed scheduler statistics and status.
    
    Returns information about:
    - Pending follow-up tasks
    - Processing status
    - Success/failure rates
    - Scheduler health
    """
    try:
        stats = await scheduler.get_scheduler_stats()
        
        return {
            "scheduler_status": "running" if scheduler.is_running else "stopped",
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting scheduler statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get scheduler statistics: {str(e)}"
        )


@router.post("/scheduler/start")
async def start_scheduler(
    scheduler = Depends(get_scheduler_dependency)
):
    """
    Manually start the scheduler (if stopped).
    
    Note: The scheduler typically starts automatically with the service.
    This endpoint is for manual control if needed.
    """
    try:
        if scheduler.is_running:
            return {
                "message": "Scheduler is already running",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
        
        await scheduler.start_scheduler()
        logger.info("✅ Scheduler started manually")
        
        return {
            "message": "Scheduler started successfully",
            "status": "running",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error starting scheduler: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start scheduler: {str(e)}"
        )


@router.post("/scheduler/stop")
async def stop_scheduler(
    scheduler = Depends(get_scheduler_dependency)
):
    """
    Manually stop the scheduler.
    
    Warning: This will stop automatic follow-up generation until restarted.
    """
    try:
        if not scheduler.is_running:
            return {
                "message": "Scheduler is already stopped",
                "status": "stopped",
                "timestamp": datetime.now().isoformat()
            }
        
        await scheduler.stop_scheduler()
        logger.info("🛑 Scheduler stopped manually")
        
        return {
            "message": "Scheduler stopped successfully",
            "status": "stopped",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error stopping scheduler: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop scheduler: {str(e)}"
        )


@router.get("/pending-followups")
async def get_pending_followups(
    scheduler = Depends(get_scheduler_dependency)
):
    """
    Get list of pending follow-up generation tasks.
    
    Returns all task sets that are scheduled for follow-up generation
    but haven't been processed yet.
    """
    try:
        pending_tasks = await scheduler.get_pending_followups()
        
        return {
            "pending_count": len(pending_tasks),
            "pending_tasks": pending_tasks,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting pending follow-ups: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pending follow-ups: {str(e)}"
        )
