"""
Follow-up Task Scheduler

Lightweight scheduling system that monitors task completion and automatically
schedules follow-up task generation when primary tasks are completed.

This scheduler:
1. Monitors task_sets for completion status changes
2. Schedules follow-up task generation for completed PRIMARY tasks
3. Uses asyncio for lightweight background processing
4. Integrates with existing Redis and database infrastructure
"""

import asyncio
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import TaskStatus, GenerationType
from app.shared.redis.redis_manager import RedisManager

# Configure logging
logger = setup_new_logging(__name__)


class FollowupScheduler:
    """
    Lightweight scheduler for follow-up task generation.
    
    Monitors task completion and schedules follow-up generation automatically.
    """
    
    def __init__(self, redis_manager: RedisManager):
        self.redis = redis_manager
        self.scheduler_key = "followup_scheduler"
        self.pending_followups_key = "pending_followups"
        self.processing_followups_key = "processing_followups"
        self.is_running = False
        self._scheduler_task = None
        
    async def start_scheduler(self):
        """Start the background scheduler."""
        if self.is_running:
            logger.warning("Follow-up scheduler is already running")
            return
            
        self.is_running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("✅ Follow-up scheduler started")
        
    async def stop_scheduler(self):
        """Stop the background scheduler."""
        self.is_running = False
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Follow-up scheduler stopped")
        
    async def schedule_followup_generation(
        self, 
        task_set_id: str, 
        user_id: str, 
        original_audio_info: Optional[Dict[str, Any]] = None,
        delay_minutes: int = 5
    ) -> bool:
        """
        Schedule follow-up task generation for a completed primary task set.
        
        Args:
            task_set_id: The completed primary task set ID
            user_id: User ID for the task set
            original_audio_info: Original audio metadata for context
            delay_minutes: Delay before generating follow-up (default: 5 minutes)
            
        Returns:
            True if successfully scheduled
        """
        try:
            schedule_time = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
            
            followup_data = {
                "task_set_id": task_set_id,
                "user_id": user_id,
                "original_audio_info": original_audio_info or {},
                "scheduled_at": datetime.now(timezone.utc).isoformat(),
                "execute_at": schedule_time.isoformat(),
                "status": "pending",
                "attempts": 0,
                "max_attempts": 3
            }
            
            # Add to pending followups with score-based priority (execute_at timestamp)
            score = schedule_time.timestamp()
            await self.redis.zadd(
                self.pending_followups_key, 
                {json.dumps(followup_data): score}
            )
            
            logger.info(f"📅 Scheduled follow-up generation for task_set {task_set_id} at {schedule_time}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule follow-up for task_set {task_set_id}: {e}")
            return False
            
    async def _scheduler_loop(self):
        """Main scheduler loop that processes pending follow-ups."""
        logger.info("🔄 Follow-up scheduler loop started")
        
        while self.is_running:
            try:
                await self._process_pending_followups()
                # Check every 30 seconds
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                logger.info("📋 Scheduler loop cancelled")
                break
            except Exception as e:
                logger.error(f"❌ Error in scheduler loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
                
    async def _process_pending_followups(self):
        """Process pending follow-up generations that are ready to execute."""
        try:
            current_time = datetime.now(timezone.utc).timestamp()
            
            # Get follow-ups ready for execution (score <= current_time)
            ready_followups = await self.redis.zrangebyscore(
                self.pending_followups_key,
                min_score=0,
                max_score=current_time,
                withscores=False,
                limit=5  # Process max 5 at a time
            )
            
            if not ready_followups:
                return
                
            logger.info(f"🎯 Processing {len(ready_followups)} ready follow-up generations")
            
            for followup_json in ready_followups:
                try:
                    followup_data = json.loads(followup_json)
                    
                    # Move to processing set
                    await self.redis.zrem(self.pending_followups_key, followup_json)
                    await self.redis.sadd(self.processing_followups_key, followup_json)
                    
                    # Process the follow-up generation
                    success = await self._generate_followup_tasks(followup_data)
                    
                    if success:
                        # Remove from processing set on success
                        await self.redis.srem(self.processing_followups_key, followup_json)
                        logger.info(f"✅ Successfully generated follow-up for task_set {followup_data['task_set_id']}")
                    else:
                        # Handle retry logic
                        await self._handle_followup_retry(followup_data, followup_json)
                        
                except Exception as e:
                    logger.error(f"❌ Error processing follow-up: {e}")
                    # Remove from processing set on error
                    await self.redis.srem(self.processing_followups_key, followup_json)
                    
        except Exception as e:
            logger.error(f"❌ Error in _process_pending_followups: {e}")
            
    async def _generate_followup_tasks(self, followup_data: Dict[str, Any]) -> bool:
        """
        Generate follow-up tasks for a completed primary task set.
        
        Args:
            followup_data: Follow-up generation data
            
        Returns:
            True if successful
        """
        try:
            task_set_id = followup_data["task_set_id"]
            user_id = followup_data["user_id"]
            
            logger.info(f"🚀 Generating follow-up tasks for task_set {task_set_id}")
            
            # Import here to avoid circular imports
            from app.v2.api.socket_service_v2.generator.followup import generate_followup_tasks
            from app.shared.models.user import UserTenantDB

            # Get user context for database access
            # TODO: This is a simplified approach - in production, you'd want to properly
            # initialize the UserTenantDB with proper tenant context
            current_user = UserTenantDB(user_id=user_id)

            # Generate follow-up tasks
            result = await generate_followup_tasks(
                task_set_id=task_set_id,
                user_id=user_id,
                current_user=current_user,
                original_audio_info=followup_data.get("original_audio_info", {})
            )
            
            if result.get("status") == "success":
                logger.info(f"✅ Follow-up tasks generated successfully for task_set {task_set_id}")
                return True
            else:
                logger.error(f"❌ Follow-up generation failed for task_set {task_set_id}: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error generating follow-up tasks: {e}")
            return False
            
    async def _handle_followup_retry(self, followup_data: Dict[str, Any], followup_json: str):
        """Handle retry logic for failed follow-up generation."""
        try:
            followup_data["attempts"] += 1
            
            if followup_data["attempts"] >= followup_data["max_attempts"]:
                # Max attempts reached, remove from processing
                await self.redis.srem(self.processing_followups_key, followup_json)
                logger.error(f"❌ Max attempts reached for follow-up task_set {followup_data['task_set_id']}")
                return
                
            # Reschedule with exponential backoff
            delay_minutes = 5 * (2 ** followup_data["attempts"])  # 10, 20, 40 minutes
            retry_time = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
            
            followup_data["execute_at"] = retry_time.isoformat()
            followup_data["status"] = "retrying"
            
            # Remove from processing and add back to pending
            await self.redis.srem(self.processing_followups_key, followup_json)
            await self.redis.zadd(
                self.pending_followups_key,
                {json.dumps(followup_data): retry_time.timestamp()}
            )
            
            logger.info(f"🔄 Rescheduled follow-up for task_set {followup_data['task_set_id']} (attempt {followup_data['attempts']}) at {retry_time}")
            
        except Exception as e:
            logger.error(f"❌ Error handling follow-up retry: {e}")
            
    async def get_scheduler_stats(self) -> Dict[str, Any]:
        """Get scheduler statistics."""
        try:
            pending_count = await self.redis.zcard(self.pending_followups_key)
            processing_count = await self.redis.scard(self.processing_followups_key)
            
            return {
                "is_running": self.is_running,
                "pending_followups": pending_count,
                "processing_followups": processing_count,
                "scheduler_status": "active" if self.is_running else "stopped"
            }
        except Exception as e:
            logger.error(f"❌ Error getting scheduler stats: {e}")
            return {"error": str(e)}


# Global scheduler instance
_scheduler_instance: Optional[FollowupScheduler] = None


async def get_followup_scheduler(redis_manager: RedisManager) -> FollowupScheduler:
    """Get or create the global follow-up scheduler instance."""
    global _scheduler_instance
    
    if _scheduler_instance is None:
        _scheduler_instance = FollowupScheduler(redis_manager)
        await _scheduler_instance.start_scheduler()
        
    return _scheduler_instance


async def schedule_followup_for_completed_task(
    task_set_id: str,
    user_id: str,
    original_audio_info: Optional[Dict[str, Any]] = None,
    redis_manager: Optional[RedisManager] = None
) -> bool:
    """
    Convenience function to schedule follow-up generation for a completed task.
    
    Args:
        task_set_id: Completed task set ID
        user_id: User ID
        original_audio_info: Original audio metadata
        redis_manager: Redis manager instance
        
    Returns:
        True if successfully scheduled
    """
    try:
        if not redis_manager:
            from app.shared.redis import get_redis_manager
            redis_manager = await get_redis_manager()
            
        scheduler = await get_followup_scheduler(redis_manager)
        return await scheduler.schedule_followup_generation(
            task_set_id=task_set_id,
            user_id=user_id,
            original_audio_info=original_audio_info
        )
    except Exception as e:
        logger.error(f"❌ Error scheduling follow-up: {e}")
        return False
