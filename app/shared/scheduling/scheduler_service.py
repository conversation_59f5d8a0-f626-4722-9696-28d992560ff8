"""
Simple Background Scheduler Service

A lightweight scheduler service that monitors task completion and automatically
schedules follow-up task generation when primary tasks are completed.

This service:
1. Starts a background task that monitors for completed task sets
2. Automatically schedules follow-up generation for PRIMARY task sets
3. Uses the existing followup_scheduler for actual processing
4. Can be started/stopped with the application lifecycle
"""

import asyncio
import logging
from typing import Optional
from datetime import datetime, timezone

from app.shared.utils.logger import setup_new_logging
from app.shared.scheduling.followup_scheduler import get_followup_scheduler
from app.shared.redis import get_redis_manager

# Configure logging
logger = setup_new_logging(__name__)

class SchedulerService:
    """Simple background scheduler service for follow-up task generation."""
    
    def __init__(self):
        self.is_running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        self._followup_scheduler = None
        
    async def start(self):
        """Start the background scheduler service."""
        if self.is_running:
            logger.warning("Scheduler service is already running")
            return
            
        try:
            # Initialize Redis and followup scheduler
            redis_manager = await get_redis_manager()
            self._followup_scheduler = await get_followup_scheduler(redis_manager)
            
            # Start the background scheduler
            await self._followup_scheduler.start_scheduler()
            
            self.is_running = True
            logger.info("🚀 Background scheduler service started successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to start scheduler service: {e}")
            raise
            
    async def stop(self):
        """Stop the background scheduler service."""
        if not self.is_running:
            logger.warning("Scheduler service is not running")
            return
            
        try:
            if self._followup_scheduler:
                await self._followup_scheduler.stop_scheduler()
                
            self.is_running = False
            logger.info("🛑 Background scheduler service stopped")
            
        except Exception as e:
            logger.error(f"❌ Error stopping scheduler service: {e}")
            
    async def schedule_followup(self, task_set_id: str, user_id: str, original_audio_info: dict = None) -> bool:
        """
        Schedule follow-up generation for a completed task set.
        
        Args:
            task_set_id: Completed task set ID
            user_id: User ID
            original_audio_info: Original audio metadata
            
        Returns:
            True if successfully scheduled
        """
        if not self.is_running or not self._followup_scheduler:
            logger.error("Scheduler service is not running")
            return False
            
        try:
            return await self._followup_scheduler.schedule_followup_generation(
                task_set_id=task_set_id,
                user_id=user_id,
                original_audio_info=original_audio_info or {}
            )
        except Exception as e:
            logger.error(f"❌ Error scheduling follow-up: {e}")
            return False

# Global scheduler service instance
_scheduler_service: Optional[SchedulerService] = None

async def get_scheduler_service() -> SchedulerService:
    """Get the global scheduler service instance."""
    global _scheduler_service
    if _scheduler_service is None:
        _scheduler_service = SchedulerService()
    return _scheduler_service

async def start_scheduler_service():
    """Start the global scheduler service."""
    service = await get_scheduler_service()
    await service.start()

async def stop_scheduler_service():
    """Stop the global scheduler service."""
    service = await get_scheduler_service()
    await service.stop()

async def schedule_followup_for_task(task_set_id: str, user_id: str, original_audio_info: dict = None) -> bool:
    """
    Convenience function to schedule follow-up generation.
    
    Args:
        task_set_id: Completed task set ID
        user_id: User ID
        original_audio_info: Original audio metadata
        
    Returns:
        True if successfully scheduled
    """
    try:
        service = await get_scheduler_service()
        return await service.schedule_followup(
            task_set_id=task_set_id,
            user_id=user_id,
            original_audio_info=original_audio_info
        )
    except Exception as e:
        logger.error(f"❌ Error scheduling follow-up for task {task_set_id}: {e}")
        return False
