from typing import Dict, Any
from app.shared.models.user import UserTenantDB
import base64
import os
from google import genai
from google.genai import types
import asyncio
from bson import ObjectId
from app.v2.api.socket_service_v2.generator.task_format import get_tasks_in_json_format
async def generate_followup_tasks(
    task_set_id: str,
    current_user: UserTenantDB,
) -> Dict[str, Any]:
    """Generate follow-up tasks for a task set."""
    tasks_info=await current_user.async_db.task_set.find_one(
        {"_id": ObjectId(task_set_id)},
    )
    audiobytes=await asyncio.to_thread(current_user.minio.get_audio_bytes(tasks_info["input_content"]["object_name"]))
    if not audiobytes:
        raise ValueError("Audio bytes not found for the given task set.")
    

    old_tasks=await get_tasks_in_json_format(
        task_set_id=tasks_info["tasks"],
        current_user=current_user,
    )
async def generate(bytes_audio):
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    contents = [
                types.Part.from_bytes(
                    mime_type="audio/mp4",
                    data=bytes_audio
                ),
            ],
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text="""You are a Nepali language tutor designing interactive quiz tasks for **{{{{age}}}}-year-old children**.
### Instructions:

You are given a **Nepali audio recording**.

Your task is to generate exactly **{{{{num_tasks}}}} quiz questions** based **only on what is clearly said in the audio**.

Do **not** add fictional details or imagined content.
Only include things **explicitly present in the audio**.

### Question Format:

Each task must follow this question-type cycle:
1. `\"single_choice\"`
2. `\"multiple_choice\"`
3. `\"image_identification\"`
4. `\"speak_word\"`

(Repeat this order if `num_tasks` > 4)

###  Quiz Question Structure:

```json
{{
\"type\": \"single_choice\" | \"multiple_choice\" | \"image_identification\" | \"speak_word\",
\"text\": \"Question in Nepali\",
\"translated_text\": \"English translation\",
\"options\": {{
\"a\": \"घोड़ा\",
\"b\": \"कुकुर\",
\"c\": \"हात्ती\"
}}, // Omit for speak_word
\"answer_hint\": \"elephant\", // or Nepali word like \"हात्ती\" for speak_word
\"answer\": \"a\" // or [\"a\", \"b\"] for multiple_choice; omit for speak_word
}}


 Guidelines:
Use simple, vivid Nepali suited for children.
All content must be directly grounded in the audio.
For \"speak_word\":

Only one Nepali word (no phrases or sentences).
For \"image_identification\":

Ask general visual questions, e.g., “What is seen in the picture?”
Do not name the object directly in the question.
Use single-word answer options (no phrases).
\"answer_hint\" must be:

In English for single_choice, multiple_choice, and image_identification.
In Nepali (one word only) for speak_word.


```json
{{
\"title\": \"सानी मायाको रमाइलो यात्रा\",

\"question\": {{
\"type\": \"single_choice\",
\"text\": \"चिरचिराउँदै को उड्दै थियो?\",
\"translated_text\": \"Who was flying while chirping?\",
\"options\": {{
\"a\": \"चरा\",
\"b\": \"बिरालो\",
\"c\": \"कुकुर\"
}},
\"answer_hint\": \"flying bird\",
\"answer\": \"a\"
}},
\"max_score\": 10,
\"complexity\": 1
}}
]
}}"""),
        ],
    )

    response=client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )

    if not response or not response.candidates:
        raise ValueError("No candidates found in the response.")
    
    tasks = response.candidates[0].content.parts[0].text
    if not tasks:
        raise ValueError("No tasks generated in the response.")
    usage = response.candidates[0].usage


